# Made in Libya Exhibition Website

A bilingual (Arabic/English) static website for the "Made in Libya" national quality exhibition event.

## Features

- **Bilingual Support**: Full Arabic and English language support with RTL/LTR text direction
- **Responsive Design**: Optimized for all device sizes (desktop, tablet, mobile)
- **Modern UI**: Clean, professional design matching the original PDF specifications
- **Libya Map Background**: Dotted pattern Libya map with subtle animations
- **Color Scheme**: Exact colors from the PDF (#4A266B, #4266AE)
- **Smooth Animations**: Fade-in effects and subtle pulsing animations
- **Language Persistence**: Remembers user's language preference

## Design Elements

### Colors
- Primary Purple: `#4A266B`
- Secondary Blue: `#4266AE`
- Gradient background from purple to blue
- White text with subtle shadows

### Typography
- Arabic: Cairo font family
- English: Inter font family
- Responsive font sizes for different screen sizes

### Layout
- Centered content with Libya map background
- Language toggle buttons in top-right corner
- Header with bilingual titles
- Main content with exhibition description

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # CSS styles and responsive design
├── script.js           # JavaScript for language switching
└── README.md           # This documentation
```

## Usage

1. Open `index.html` in a web browser
2. Use the language toggle buttons (العربية/English) to switch languages
3. The website automatically adapts to the selected language with proper text direction

## Local Development

To run locally:

```bash
# Using Python 3
python3 -m http.server 8000

# Using Node.js
npx serve .

# Using PHP
php -S localhost:8000
```

Then open `http://localhost:8000` in your browser.

## Content

The website includes:

- **Arabic Title**: "جودة وطنية بأسواق واعدة"
- **English Title**: "National quality in promising markets"
- **Main Content**: Information about the Made in Libya exhibition
- **Description**: Details about the exhibition's goals and organization

## Browser Support

- Modern browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Chrome Mobile)
- Supports RTL text direction for Arabic content

## Customization

To modify content:
1. Edit the `translations` object in `script.js` for text content
2. Modify `styles.css` for visual styling
3. Update `index.html` for structural changes

## Credits

Created for the "Made in Libya" national quality exhibition event, organized by Ajyal Company for Media Services, Exhibitions and Conferences in Benghazi, Libya.
