/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Cairo", "Inter", sans-serif;
  background: linear-gradient(135deg, #4a266b 0%, #4266ae 100%);
  min-height: 100vh;
  color: white;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: "";
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  pointer-events: none;
  z-index: 0;
}

/* Language Toggle */
.language-toggle {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  display: flex;
  gap: 10px;
}

.lang-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 25px;
  cursor: pointer;
  font-family: inherit;
  font-weight: 500;
  transition: all 0.3s ease;
}

.lang-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.lang-btn.active {
  background: rgba(255, 255, 255, 0.9);
  color: #4a266b;
  border-color: white;
}

/* Libya Map Background */
.libya-map {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600px;
  height: 400px;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.4) 2px,
    transparent 2px
  );
  background-size: 12px 12px;
  opacity: 0.5;
  z-index: 1;
  animation: pulse 4s ease-in-out infinite;
  clip-path: polygon(
    15% 20%,
    20% 15%,
    25% 12%,
    30% 10%,
    40% 8%,
    50% 10%,
    60% 12%,
    70% 15%,
    80% 20%,
    85% 30%,
    90% 40%,
    92% 50%,
    90% 60%,
    85% 70%,
    80% 80%,
    70% 85%,
    60% 88%,
    50% 90%,
    40% 88%,
    30% 85%,
    20% 80%,
    15% 70%,
    10% 60%,
    8% 50%,
    10% 40%,
    12% 30%,
    15% 20%
  );
}

.libya-map::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.6) 1px,
    transparent 1px
  );
  background-size: 8px 8px;
  background-position: 4px 4px;
  opacity: 0.7;
}

/* Container */
.container {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 40px 20px;
  animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.5;
  }
  50% {
    opacity: 0.8;
  }
}

/* Navigation */
.navigation {
  margin-bottom: 40px;
  z-index: 10;
  position: relative;
}

.nav-list {
  display: flex;
  justify-content: center;
  list-style: none;
  gap: 30px;
  flex-wrap: wrap;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 1rem;
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
}

.nav-link.active {
  background: rgba(255, 255, 255, 0.9);
  color: #4a266b;
  border-color: white;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 40px;
}

.logo-section {
  max-width: 800px;
  margin: 0 auto;
}

.arabic-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 10px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  line-height: 1.2;
}

.english-subtitle {
  font-size: 1.5rem;
  font-weight: 400;
  opacity: 0.9;
  font-family: "Inter", sans-serif;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* Main Content */
.main-content {
  flex: 1;
  position: relative;
}

.section {
  display: flex;
  min-height: auto;
  align-items: center;
  justify-content: center;
  padding: 60px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section:last-child {
  border-bottom: none;
}

.content-wrapper {
  max-width: 1000px;
  text-align: center;
  padding: 0 20px;
  margin: 0 auto;
}

.main-title {
  font-size: 2.8rem;
  font-weight: 700;
  line-height: 1.4;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.description-text {
  font-size: 1.3rem;
  line-height: 1.8;
  font-weight: 400;
  opacity: 0.95;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.description-text p {
  margin-bottom: 25px;
}

.description-text p:last-child {
  margin-bottom: 0;
}

/* Section Titles */
.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 40px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  color: white;
}

/* Vision and Message Content */
.vision-content,
.message-content {
  max-width: 800px;
  margin: 0 auto;
}

.vision-text,
.message-text {
  font-size: 1.4rem;
  line-height: 1.8;
  font-weight: 400;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Objectives Grid */
.objectives-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 40px;
}

.objective-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.objective-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.objective-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.objective-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* Companies Section */
.companies-content {
  max-width: 800px;
  margin: 0 auto;
}

.companies-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.company-category {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.company-category:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.category-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 15px;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.category-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.companies-highlight {
  text-align: center;
  background: rgba(255, 215, 0, 0.1);
  border: 2px solid rgba(255, 215, 0, 0.3);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(10px);
}

.highlight-title {
  font-size: 2rem;
  font-weight: 700;
  color: #ffd700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin: 0;
}

/* Audience Section */
.audience-content {
  max-width: 900px;
  margin: 0 auto;
}

.audience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 25px;
  margin-bottom: 50px;
}

.audience-item {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.audience-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.audience-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0;
  line-height: 1.5;
}

.exhibitors-section {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 30px;
  text-align: center;
  backdrop-filter: blur(10px);
}

.exhibitors-description {
  font-size: 1.2rem;
  line-height: 1.7;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 20px 0 0 0;
}

/* Visitors Section */
.visitors-section {
  margin-top: 50px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid rgba(255, 255, 255, 0.15);
  border-radius: 20px;
  padding: 40px;
  backdrop-filter: blur(10px);
}

.visitors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.visitor-category {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 25px;
  text-align: right;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.visitor-category:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-5px);
}

.visitor-title {
  font-size: 1.3rem;
  font-weight: 600;
  color: #ffd700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
  margin: 0 0 15px 0;
  line-height: 1.4;
}

.visitor-description {
  font-size: 1rem;
  line-height: 1.6;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
  margin: 0;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .header,
[dir="rtl"] .content-wrapper {
  text-align: center;
}

[dir="ltr"] {
  font-family: "Inter", "Cairo", sans-serif;
}

[dir="rtl"] {
  font-family: "Cairo", "Inter", sans-serif;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .libya-map {
    width: 500px;
    height: 350px;
  }

  .arabic-title {
    font-size: 2.2rem;
  }

  .main-title {
    font-size: 2.4rem;
  }
}

@media (max-width: 768px) {
  .arabic-title {
    font-size: 1.8rem;
    line-height: 1.3;
  }

  .english-subtitle {
    font-size: 1.1rem;
  }

  .main-title {
    font-size: 1.8rem;
    line-height: 1.3;
    margin-bottom: 30px;
  }

  .description-text {
    font-size: 1rem;
    line-height: 1.7;
  }

  .libya-map {
    width: 350px;
    height: 250px;
  }

  .container {
    padding: 20px 15px;
  }

  .header {
    margin-bottom: 30px;
  }

  .navigation {
    margin-bottom: 30px;
  }

  .nav-list {
    gap: 15px;
  }

  .nav-link {
    padding: 10px 18px;
    font-size: 0.9rem;
  }

  .section-title {
    font-size: 2rem;
    margin-bottom: 30px;
  }

  .vision-text,
  .message-text {
    font-size: 1.2rem;
    line-height: 1.7;
  }

  .objectives-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .objective-item {
    padding: 20px;
  }

  .objective-title {
    font-size: 1.2rem;
  }

  .objective-description {
    font-size: 0.95rem;
  }

  .companies-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .company-category {
    padding: 20px;
  }

  .category-title {
    font-size: 1.2rem;
  }

  .highlight-title {
    font-size: 1.7rem;
  }

  .audience-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .audience-item {
    padding: 20px;
  }

  .audience-title {
    font-size: 1.1rem;
  }

  .exhibitors-description {
    font-size: 1.1rem;
  }

  .visitors-section {
    margin-top: 30px;
    padding: 30px;
  }

  .visitors-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .visitor-category {
    padding: 20px;
  }

  .visitor-title {
    font-size: 1.2rem;
  }

  .visitor-description {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .arabic-title {
    font-size: 1.6rem;
  }

  .english-subtitle {
    font-size: 1rem;
  }

  .main-title {
    font-size: 1.5rem;
  }

  .description-text {
    font-size: 1rem;
    line-height: 1.6;
  }

  .libya-map {
    width: 300px;
    height: 200px;
  }

  .language-toggle {
    top: 10px;
    right: 10px;
  }

  .lang-btn {
    padding: 6px 12px;
    font-size: 0.9rem;
  }

  .nav-list {
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .nav-link {
    padding: 8px 16px;
    font-size: 0.85rem;
    min-width: 120px;
    text-align: center;
  }

  .section-title {
    font-size: 1.7rem;
    margin-bottom: 25px;
  }

  .vision-text,
  .message-text {
    font-size: 1.1rem;
    line-height: 1.6;
  }

  .objective-item {
    padding: 15px;
  }

  .objective-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .objective-description {
    font-size: 0.9rem;
    line-height: 1.5;
  }

  .objectives-grid {
    margin-top: 25px;
    gap: 15px;
  }

  .container {
    padding: 15px 10px;
  }

  .content-wrapper {
    padding: 0 10px;
  }

  .companies-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 30px;
  }

  .company-category {
    padding: 15px;
  }

  .category-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .category-description {
    font-size: 0.9rem;
  }

  .companies-highlight {
    padding: 20px;
  }

  .highlight-title {
    font-size: 1.5rem;
  }

  .audience-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-bottom: 30px;
  }

  .audience-item {
    padding: 15px;
  }

  .audience-title {
    font-size: 1rem;
  }

  .exhibitors-section {
    padding: 20px;
  }

  .exhibitors-description {
    font-size: 1rem;
    line-height: 1.6;
  }

  .visitors-section {
    margin-top: 20px;
    padding: 20px;
  }

  .visitors-grid {
    grid-template-columns: 1fr;
    gap: 15px;
    margin-top: 20px;
  }

  .visitor-category {
    padding: 15px;
  }

  .visitor-title {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .visitor-description {
    font-size: 0.9rem;
    line-height: 1.5;
  }
}
