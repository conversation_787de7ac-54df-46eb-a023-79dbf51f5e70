// Language switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const langAr = document.getElementById('lang-ar');
    const langEn = document.getElementById('lang-en');
    const html = document.documentElement;
    const body = document.body;

    // Translation content
    const translations = {
        ar: {
            title: "معرض \"صنع في ليبيا\" - Made in Libya Exhibition",
            headerArabic: "جودة وطنية بأسواق واعدة",
            headerEnglish: "National quality in promising markets",
            mainTitle: "يُعد معرض \"صنع في ليبيا\" منصة وطنية حيوية تهدف إلى تسليط الضوء على قدرات وإمكانيات القطاع الصناعي والإنتاجي الليبي",
            description: [
                "تعزيز ثقافة المنتج المحلي لدى المستهلكين، وفتح آفاق جديدة للشركات والمصانع الليبية نحو النمو والتوسع.",
                "تنظيم هذا المعرض، في مدينة بنغازي، العاصمة الاقتصادية والتجارية الثانية لليبيا، ليؤكد على أهمية المدينة كمركز حيوي للنشاط الاقتصادي وناقذة للمنتجات الليبية نحو الأسواق المحلية والإقليمية.",
                "شركة أجيال للخدمات الإعلامية والمعارض والمؤتمرات، بخبرتها الواسعة، مسؤولة تنظم هذا الحدث الهام لضمان تقديمه بأعلى معايير الجودة والاحترافية"
            ]
        },
        en: {
            title: "Made in Libya Exhibition - معرض \"صنع في ليبيا\"",
            headerArabic: "National quality in promising markets",
            headerEnglish: "جودة وطنية بأسواق واعدة",
            mainTitle: "The \"Made in Libya\" exhibition is a vital national platform aimed at highlighting the capabilities and potential of the Libyan industrial and productive sector",
            description: [
                "Promoting the culture of local products among consumers, and opening new horizons for Libyan companies and factories towards growth and expansion.",
                "Organizing this exhibition in the city of Benghazi, Libya's second economic and commercial capital, confirms the importance of the city as a vital center for economic activity and a gateway for Libyan products to local and regional markets.",
                "Ajyal Company for Media Services, Exhibitions and Conferences, with its extensive experience, is responsible for organizing this important event to ensure its delivery to the highest standards of quality and professionalism."
            ]
        }
    };

    // Function to switch language
    function switchLanguage(lang) {
        const isArabic = lang === 'ar';
        
        // Update HTML attributes
        html.setAttribute('lang', lang);
        html.setAttribute('dir', isArabic ? 'rtl' : 'ltr');
        
        // Update document title
        document.title = translations[lang].title;
        
        // Update button states
        langAr.classList.toggle('active', isArabic);
        langEn.classList.toggle('active', !isArabic);
        
        // Update content
        const arabicTitle = document.querySelector('.arabic-title');
        const englishSubtitle = document.querySelector('.english-subtitle');
        const mainTitle = document.querySelector('.main-title');
        const descriptionText = document.querySelector('.description-text');
        
        if (arabicTitle) arabicTitle.textContent = translations[lang].headerArabic;
        if (englishSubtitle) englishSubtitle.textContent = translations[lang].headerEnglish;
        if (mainTitle) mainTitle.textContent = translations[lang].mainTitle;
        
        if (descriptionText) {
            descriptionText.innerHTML = translations[lang].description
                .map(text => `<p>${text}</p>`)
                .join('');
        }
        
        // Store language preference
        localStorage.setItem('preferred-language', lang);
    }

    // Event listeners
    langAr.addEventListener('click', () => switchLanguage('ar'));
    langEn.addEventListener('click', () => switchLanguage('en'));

    // Initialize with saved language or default to Arabic
    const savedLang = localStorage.getItem('preferred-language') || 'ar';
    switchLanguage(savedLang);

    // Smooth scroll animation for better UX
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Add fade-in animation on load
    body.style.opacity = '0';
    body.style.transition = 'opacity 0.5s ease-in-out';
    
    setTimeout(() => {
        body.style.opacity = '1';
    }, 100);
});
